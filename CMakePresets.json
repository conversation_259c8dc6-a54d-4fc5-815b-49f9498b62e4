{"version": 3, "configurePresets": [{"name": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>g", "description": "Default build using Ninja generator", "generator": "Ninja", "binaryDir": "${sourceDir}/build/${presetName}", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_EXPORT_COMPILE_COMMANDS": "ON"}}, {"name": "Debug", "displayName": "Debug", "description": "Debug build with all debugging features", "inherits": "<PERSON><PERSON><PERSON>", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "Release", "displayName": "Release", "description": "Release build optimized for performance", "inherits": "<PERSON><PERSON><PERSON>", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"name": "RelWithDebInfo", "displayName": "RelWithDebInfo", "description": "Release build with Debug information", "inherits": "<PERSON><PERSON><PERSON>", "cacheVariables": {"CMAKE_BUILD_TYPE": "RelWithDebInfo"}}], "buildPresets": [{"name": "<PERSON><PERSON><PERSON>", "configurePreset": "<PERSON><PERSON><PERSON>"}, {"name": "Debug", "configurePreset": "Debug"}, {"name": "Release", "configurePreset": "Release"}, {"name": "RelWithDebInfo", "configurePreset": "RelWithDebInfo"}], "testPresets": [{"name": "<PERSON><PERSON><PERSON>", "configurePreset": "<PERSON><PERSON><PERSON>", "output": {"outputOnFailure": true}}, {"name": "Debug", "configurePreset": "Debug", "output": {"outputOnFailure": true}}, {"name": "Release", "configurePreset": "Release", "output": {"outputOnFailure": true}}]}