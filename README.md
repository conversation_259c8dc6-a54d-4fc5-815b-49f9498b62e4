A C++23 project template with Conan and vcpkg package management support and Catch2 testing.

### Requires
- **CMake 3.25+**
- **C++23 compiler**
- **Package Manager** (choose one):
  - **Conan 2.x** (default) 
  - **vcpkg** - (in cmake/dependencies.cmake)

### Setup

```bash
# 1. Clone and customize
git clone https://github.com/DjXray30/cmake-template.git

# 2. Update project name in CMakeLists.txt 
# Change "MyProject" to your actual project name

# 3. Build and run
cmake --preset Debug
cmake --build build/Debug
./build/Debug/src/MyProject_app

# 4. Run tests
cmake --build build/Debug --target test
```

## 📁 Project Structure

```
├── CMakeLists.txt
├── CMakePresets.json
├── conanfile.py
├── vcpkg.json
├── cmake/
│   ├── conan-helper.cmake
│   ├── vcpkg-helper.cmake
│   ├── dependencies.cmake
│   ├── project-options.cmake
│   ├── project-targets.cmake
│   ├── project-warnings.cmake
│   └── testing.cmake
├── src/
│   ├── CMakeLists.txt
│   └── main.cpp
└── test/
    ├── CMakeLists.txt
    └── test_main.cpp
```

## 🔧 Build & Test

### Build Configurations

| Preset | Command |
|--------|---------|
| `Debug` | `cmake --preset Debug` |
| `Release` | `cmake --preset Release` |
| `RelWithDebInfo` | `cmake --preset RelWithDebInfo` |

### Building

```bash
# Configure
cmake --preset Debug
# Build
cmake --build build/Debug

# Different configurations
cmake --preset Release && cmake --build build/Release
```

### Testing with Catch2

```bash
# Run all tests
cmake --build build/Debug --target test

# Run tests with output
cd build/Debug && ctest --output-on-failure

# Run test executable
./build/Debug/test/MyProject_tests
```

## IDE / Editor

### VS Code + clangd (Recommended)
1. Install **clangd extension**
2. Build project once: `cmake --preset Debug && cmake --build build/Debug`
Note: generates `compile_commands.json` symlink for clangd language server

### CLion
1. Open project folder
2. CLion auto-detects CMake configuration
3. Select preset from CMake profiles dropdown

### Other IDEs
Any IDE supporting `compile_commands.json` works automatically (Qt Creator, Code::Blocks, etc.)

### Switching to Vcpkg
1. Edit `cmake/dependencies.cmake`:
   ```cmake
   # --- Conan Integration ---
   # include(cmake/conan-helper.cmake)  # Comment this line

   # --- Vcpkg Integration ---
   include(cmake/vcpkg-helper.cmake)  # Uncomment this line
   ```
2. Install dependencies: `conan install . --build=missing`
3. Dependencies are defined in `conanfile.py`

## Usage

### 1. Rename Project
Edit `CMakeLists.txt` line 4-8:
```cmake
project(
  YourProjectName              # Change this
  VERSION 1.0.0
  DESCRIPTION "Your description"
  LANGUAGES CXX
)
```

### 2. Add Source Files
Edit `src/CMakeLists.txt`:
```cmake
add_executable(${PROJECT_NAME}_app
    main.cpp
    your_file.cpp               # Add new files
    another_file.cpp
)
```

### 3. Add Dependencies

#### For vcpkg (default):
Edit `vcpkg.json`:
```json
{
  "name": "my-project",
  "version": "1.0.0",
  "dependencies": [
    "catch2",
    "fmt",           // Add new dependencies
    "spdlog"
  ]
}
```

#### For Conan:
Edit `conanfile.py`:
```python
def requirements(self):
    self.requires("catch2/3.8.1")
    self.requires("fmt/10.2.1")      # Add new dependencies
    self.requires("spdlog/1.12.0")
```

#### For both package managers:
Edit `cmake/dependencies.cmake`:
```cmake
find_package(fmt REQUIRED)
find_package(spdlog REQUIRED)
```

### 4. Link Dependencies
Update `src/CMakeLists.txt`:
```cmake
target_link_libraries(${PROJECT_NAME}_app
  PRIVATE
    ${PROJECT_NAME}_options
    ${PROJECT_NAME}_warnings
    fmt::fmt                    # Link new dependencies
    spdlog::spdlog
)
```
