# --- Conan integration: <PERSON> installs packages, includes Conan toolchain file ---

set(CONAN_TOOLCHAIN_FILE "${CMAKE_BINARY_DIR}/generators/conan_toolchain.cmake")

# <PERSON> runs "conan install" if needed
if(NOT EXISTS "${CONAN_TOOLCHAIN_FILE}")
    message(STATUS "Conan toolchain not found. Running conan install for build type: ${CMAKE_BUILD_TYPE}")
    
    execute_process(
        COMMAND conan install ${CMAKE_SOURCE_DIR} 
                --build=missing
                -s build_type=${CMAKE_BUILD_TYPE}
        RESULT_VARIABLE CONAN_RESULT
        OUTPUT_VARIABLE CONAN_OUTPUT
        ERROR_VARIABLE CONAN_ERROR
        OUTPUT_STRIP_TRAILING_WHITESPACE
        ERROR_STRIP_TRAILING_WHITESPACE
    )
    
    if(NOT CONAN_RESULT EQUAL 0)
        message(FATAL_ERROR "Conan install failed with exit code ${CONAN_RESULT}:\n${CONAN_ERROR}")
    endif()
    
    message(STATUS "Conan install completed successfully")
endif()

# Load toolchain
if(EXISTS "${CONAN_TOOLCHAIN_FILE}")
    include(${CONAN_TOOLCHAIN_FILE})
endif()
