# --- vcpkg integration: Auto installs packages, includes vcpkg toolchain file ---

# Set vcpkg root directory 
if(NOT DEFINED VCPKG_ROOT AND DEFINED ENV{VCPKG_ROOT})
    set(VCPKG_ROOT $ENV{VCPKG_ROOT})
endif()

if(NOT VCPKG_ROOT)
    message(FATAL_ERROR "VCPKG_ROOT not set. Please set VCPKG_ROOT environment variable or pass -DVCPKG_ROOT=<path> to CMake")
endif()

# Set the vcpkg toolchain file path
set(VCPKG_TOOLCHAIN_FILE "${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake")

# Verify vcpkg installation exists
if(NOT EXISTS "${VCPKG_TOOLCHAIN_FILE}")
    message(FATAL_ERROR "vcpkg toolchain not found at: ${VCPKG_TOOLCHAIN_FILE}")
endif()

# Check for vcpkg.json manifest file
set(VCPKG_MANIFEST_FILE "${CMAKE_SOURCE_DIR}/vcpkg.json")
if(EXISTS "${VCPKG_MANIFEST_FILE}")
    message(STATUS "Found vcpkg manifest: ${VCPKG_MANIFEST_FILE}")
    
    # Set manifest mode
    set(VCPKG_MANIFEST_MODE ON)
    
    # Auto install dependencies if vcpkg_installed directory doesn't exist or is outdated
    set(VCPKG_INSTALLED_DIR "${CMAKE_SOURCE_DIR}/vcpkg_installed")
    set(FORCE_VCPKG_INSTALL FALSE)
    
    # Check if we need to run vcpkg install
    if(NOT EXISTS "${VCPKG_INSTALLED_DIR}")
        set(FORCE_VCPKG_INSTALL TRUE)
        message(STATUS "vcpkg_installed directory not found")
    else()
        # Check if manifest is newer than installed directory
        file(TIMESTAMP "${VCPKG_MANIFEST_FILE}" MANIFEST_TIME)
        file(TIMESTAMP "${VCPKG_INSTALLED_DIR}" INSTALLED_TIME)
        if(MANIFEST_TIME IS_NEWER_THAN INSTALLED_TIME)
            set(FORCE_VCPKG_INSTALL TRUE)
            message(STATUS "vcpkg.json is newer than vcpkg_installed directory")
        endif()
    endif()
    
    if(FORCE_VCPKG_INSTALL)
        # Determine triplet if not set
        if(NOT VCPKG_TARGET_TRIPLET)
            if(WIN32)
                if(CMAKE_SIZEOF_VOID_P EQUAL 8)
                    set(VCPKG_TARGET_TRIPLET "x64-windows")
                else()
                    set(VCPKG_TARGET_TRIPLET "x86-windows")
                endif()
            elseif(APPLE)
                set(VCPKG_TARGET_TRIPLET "x64-osx")
            else()
                set(VCPKG_TARGET_TRIPLET "x64-linux")
            endif()
        endif()
        
        message(STATUS "Running vcpkg install for triplet: ${VCPKG_TARGET_TRIPLET}")
        
        # Build vcpkg executable path
        set(VCPKG_EXECUTABLE "${VCPKG_ROOT}/vcpkg")
        if(WIN32)
            set(VCPKG_EXECUTABLE "${VCPKG_ROOT}/vcpkg.exe")
        endif()
        
        # Verify vcpkg executable exists
        if(NOT EXISTS "${VCPKG_EXECUTABLE}")
            message(FATAL_ERROR "vcpkg executable not found at: ${VCPKG_EXECUTABLE}")
        endif()
        
        message(STATUS "Executing: ${VCPKG_EXECUTABLE} install --triplet ${VCPKG_TARGET_TRIPLET}")
        
        execute_process(
            COMMAND "${VCPKG_EXECUTABLE}" install --triplet ${VCPKG_TARGET_TRIPLET}
            WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
            RESULT_VARIABLE VCPKG_RESULT
            OUTPUT_VARIABLE VCPKG_OUTPUT
            ERROR_VARIABLE VCPKG_ERROR
            OUTPUT_STRIP_TRAILING_WHITESPACE
            ERROR_STRIP_TRAILING_WHITESPACE
        )
        
        if(NOT VCPKG_RESULT EQUAL 0)
            message(STATUS "vcpkg output: ${VCPKG_OUTPUT}")
            message(FATAL_ERROR "vcpkg install failed with exit code ${VCPKG_RESULT}:\n${VCPKG_ERROR}")
        endif()
        
        message(STATUS "vcpkg install completed successfully")
    else()
        message(STATUS "vcpkg dependencies are up to date")
    endif()
else()
    message(STATUS "No vcpkg.json manifest found. Using classic mode.")
    set(VCPKG_MANIFEST_MODE OFF)
endif()

# Load vcpkg toolchain
message(STATUS "Loading vcpkg toolchain: ${VCPKG_TOOLCHAIN_FILE}")
include(${VCPKG_TOOLCHAIN_FILE})

# Optional: Set common vcpkg variables
if(NOT DEFINED VCPKG_FEATURE_FLAGS)
    set(VCPKG_FEATURE_FLAGS "manifests,versions,binarycaching")
endif()
